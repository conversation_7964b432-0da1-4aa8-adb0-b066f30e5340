package cn.powerchina.bjy.link.dam.service.mqtt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.config.MqttConfig;
import cn.powerchina.bjy.link.iot.api.transportTarget.TransportTargetApi;
import cn.powerchina.bjy.link.iot.api.transportTarget.dto.TransportTargetRespDTO;
import cn.powerchina.bjy.link.iot.enums.TransportTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: MQTT服务
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Service
public interface MqttService {


    public void publishMessage(String topic, String message, int qos, boolean retained);


    public void publishMessage(String topic, String message);

    public void subscribe(String topic, int qos);

    public void subscribe(String topic);

    public void unsubscribe(String topic);

    public List<String> getAvailableTopics();

    public void subscribeToAllAvailableTopics();

    public boolean isConnected();


}
