package cn.powerchina.bjy.link.dam.service.mqtt;

import java.util.List;

/**
 * @Description: MQTT服务接口
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
public interface MqttService {

    /**
     * 发布消息到指定主题
     *
     * @param topic   主题
     * @param message 消息内容
     * @param qos     服务质量等级 (0, 1, 2)
     * @param retained 是否保留消息
     */
    void publishMessage(String topic, String message, int qos, boolean retained);

    /**
     * 发布消息到指定主题（使用默认QoS和retained设置）
     *
     * @param topic   主题
     * @param message 消息内容
     */
    void publishMessage(String topic, String message);

    /**
     * 订阅主题
     *
     * @param topic 主题
     * @param qos   服务质量等级
     */
    void subscribe(String topic, int qos);

    /**
     * 订阅主题（使用默认QoS）
     *
     * @param topic 主题
     */
    void subscribe(String topic);

    /**
     * 取消订阅主题
     *
     * @param topic 主题
     */
    void unsubscribe(String topic);

    /**
     * 获取所有可用的传输目标主题
     *
     * @return 主题列表
     */
    List<String> getAvailableTopics();

    /**
     * 订阅所有可用的传输目标主题
     */
    void subscribeToAllAvailableTopics();

    /**
     * 检查MQTT连接状态
     *
     * @return 是否已连接
     */
    boolean isConnected();
}
