package cn.powerchina.bjy.link.dam.service.mqtt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.config.MqttConfig;
import cn.powerchina.bjy.link.iot.api.transportTarget.TransportTargetApi;
import cn.powerchina.bjy.link.iot.api.transportTarget.dto.TransportTargetRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: MQTT服务实现类
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Service
@Slf4j
public class MqttServiceImpl implements MqttService {

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private MqttPahoClientFactory mqttClientFactory;

    @Autowired
    private MessageChannel mqttOutboundChannel;

    @Autowired
    private TransportTargetApi transportTargetApi;

    private MqttPahoMessageDrivenChannelAdapter inboundAdapter;

    @PostConstruct
    public void init() {
        log.info("初始化MQTT服务...");
        try {
            // 初始化入站适配器
            initInboundAdapter();

            // 订阅自动订阅的主题
            subscribeAutoTopics();

            log.info("MQTT服务初始化完成");
        } catch (Exception e) {
            log.error("MQTT服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("销毁MQTT服务...");
        if (inboundAdapter != null) {
            try {
                inboundAdapter.stop();
                log.info("MQTT入站适配器已停止");
            } catch (Exception e) {
                log.error("停止MQTT入站适配器时发生错误", e);
            }
        }
    }

    @Override
    public void publishMessage(String topic, String message, int qos, boolean retained) {
        try {
            log.info("发布MQTT消息 - Topic: {}, Message: {}, QoS: {}, Retained: {}",
                    topic, message, qos, retained);

            org.springframework.messaging.Message<String> mqttMessage = MessageBuilder
                    .withPayload(message)
                    .setHeader(MqttHeaders.TOPIC, topic)
                    .setHeader(MqttHeaders.QOS, qos)
                    .setHeader(MqttHeaders.RETAINED, retained)
                    .build();

            mqttOutboundChannel.send(mqttMessage);
            log.info("MQTT消息发布成功 - Topic: {}", topic);

        } catch (Exception e) {
            log.error("发布MQTT消息失败 - Topic: {}, Message: {}", topic, message, e);
            throw new RuntimeException("发布MQTT消息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void publishMessage(String topic, String message) {
        publishMessage(topic, message, mqttConfig.getDefaultQos(), mqttConfig.isDefaultRetained());
    }

    @Override
    public void subscribe(String topic, int qos) {
        try {
            log.info("订阅MQTT主题 - Topic: {}, QoS: {}", topic, qos);

            if (inboundAdapter != null) {
                inboundAdapter.addTopic(topic, qos);
                log.info("成功订阅MQTT主题 - Topic: {}", topic);
            } else {
                log.warn("MQTT入站适配器未初始化，无法订阅主题: {}", topic);
            }

        } catch (Exception e) {
            log.error("订阅MQTT主题失败 - Topic: {}", topic, e);
            throw new RuntimeException("订阅MQTT主题失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void subscribe(String topic) {
        subscribe(topic, mqttConfig.getDefaultSubscribeQos());
    }

    @Override
    public void unsubscribe(String topic) {
        try {
            log.info("取消订阅MQTT主题 - Topic: {}", topic);

            if (inboundAdapter != null) {
                inboundAdapter.removeTopic(topic);
                log.info("成功取消订阅MQTT主题 - Topic: {}", topic);
            } else {
                log.warn("MQTT入站适配器未初始化，无法取消订阅主题: {}", topic);
            }

        } catch (Exception e) {
            log.error("取消订阅MQTT主题失败 - Topic: {}", topic, e);
            throw new RuntimeException("取消订阅MQTT主题失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getAvailableTopics() {
        try {
            log.info("获取可用的传输目标主题...");

            CommonResult<List<TransportTargetRespDTO>> result = transportTargetApi.getTransportTarget(null);

            if (result == null || !result.isSuccess() || result.getData() == null) {
                log.warn("获取传输目标失败或返回空数据");
                return new ArrayList<>();
            }

            List<String> topics = result.getData().stream()
                    .filter(target -> target.getTopic() != null && !target.getTopic().trim().isEmpty())
                    .map(TransportTargetRespDTO::getTopic)
                    .distinct()
                    .collect(Collectors.toList());

            log.info("获取到 {} 个主题: {}", topics.size(), topics);
            return topics;

        } catch (Exception e) {
            log.error("获取可用主题失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public void subscribeToAllAvailableTopics() {
        try {
            log.info("订阅所有可用的传输目标主题...");

            List<String> topics = getAvailableTopics();

            if (topics.isEmpty()) {
                log.info("没有可用的主题需要订阅");
                return;
            }

            for (String topic : topics) {
                try {
                    subscribe(topic);
                } catch (Exception e) {
                    log.error("订阅主题失败: {}", topic, e);
                }
            }

            log.info("完成订阅所有可用主题，共 {} 个", topics.size());

        } catch (Exception e) {
            log.error("订阅所有可用主题失败", e);
        }
    }

    @Override
    public boolean isConnected() {
        try {
            return inboundAdapter != null && inboundAdapter.isRunning();
        } catch (Exception e) {
            log.error("检查MQTT连接状态失败", e);
            return false;
        }
    }

    /**
     * 初始化入站适配器
     */
    private void initInboundAdapter() {
        try {
            String clientId = mqttConfig.getFullClientId();
            inboundAdapter = new MqttPahoMessageDrivenChannelAdapter(clientId, mqttClientFactory);
            inboundAdapter.setCompletionTimeout(5000);
            inboundAdapter.setQos(mqttConfig.getDefaultSubscribeQos());
            inboundAdapter.setOutputChannelName("mqttInputChannel");

            // 启动适配器
            inboundAdapter.start();

            log.info("MQTT入站适配器初始化成功 - ClientId: {}", clientId);

        } catch (Exception e) {
            log.error("初始化MQTT入站适配器失败", e);
            throw new RuntimeException("初始化MQTT入站适配器失败: " + e.getMessage(), e);
        }
    }

    /**
     * 订阅自动订阅的主题
     */
    private void subscribeAutoTopics() {
        try {
            String autoTopics = mqttConfig.getAutoSubscribeTopics();

            if (StringUtils.hasText(autoTopics)) {
                String[] topics = autoTopics.split(",");
                for (String topic : topics) {
                    topic = topic.trim();
                    if (StringUtils.hasText(topic)) {
                        subscribe(topic);
                    }
                }
                log.info("自动订阅主题完成，共 {} 个主题", topics.length);
            }

            // 订阅所有可用的传输目标主题
            subscribeToAllAvailableTopics();

        } catch (Exception e) {
            log.error("订阅自动主题失败", e);
        }
    }
}
