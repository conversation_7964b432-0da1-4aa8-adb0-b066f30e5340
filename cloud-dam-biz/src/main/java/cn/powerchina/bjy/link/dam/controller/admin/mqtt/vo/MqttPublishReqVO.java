package cn.powerchina.bjy.link.dam.controller.admin.mqtt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description: MQTT消息发布请求VO
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Schema(description = "管理后台 - MQTT消息发布请求 VO")
@Data
public class MqttPublishReqVO {

    @Schema(description = "主题", requiredMode = Schema.RequiredMode.REQUIRED, example = "dam/data/sensor1")
    @NotBlank(message = "主题不能为空")
    private String topic;

    @Schema(description = "消息内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "{\"temperature\": 25.5, \"humidity\": 60}")
    @NotBlank(message = "消息内容不能为空")
    private String message;

    @Schema(description = "QoS等级 (0, 1, 2)", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    @Min(value = 0, message = "QoS等级最小为0")
    @Max(value = 2, message = "QoS等级最大为2")
    private Integer qos = 1;

    @Schema(description = "是否保留消息", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "false")
    private Boolean retained = false;
}
